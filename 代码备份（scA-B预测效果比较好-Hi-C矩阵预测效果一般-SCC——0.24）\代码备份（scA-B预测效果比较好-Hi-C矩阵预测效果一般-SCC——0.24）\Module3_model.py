# Modified Module3_model.py
import torch
import torch.nn as nn
import torch.nn.functional as F
import lightning.pytorch as pl
from torch.utils.data import Dataset, DataLoader
import anndata as ad
import pandas as pd
import numpy as np
from scipy.sparse import issparse
import argparse
from torch.utils.data import random_split
from Module1_model import *
from Module3_utils import *

class MultiTaskDataset(Dataset):
    def __init__(self, input_path, target_csv_path, target_h5ad_path, patch_size, mode='train'):
        """
        mode: 'train' for training split, 'val' for validation split, 'test' for independent test set (no split, use all common_ids)
        """
        input_ad = ad.read_h5ad(input_path)
        target_ad = ad.read_h5ad(target_h5ad_path)
        target_ad.obs = target_ad.obs.set_index('cell_id')
        target_df = pd.read_csv(target_csv_path)

        # Remove first two columns (assuming they are chrom and bin)
        self.target_df_prefix = target_df.iloc[:, :2]  # Save prefix columns for later reconstruction if needed
        target_df = target_df.iloc[:, 2:]
        # Create dict where key is ID (column name), value is the entire column as numpy array
        scab_dict = {col: target_df[col].values for col in target_df.columns}

        # Get common IDs
        common_ids = set(input_ad.obs.index) & set(target_ad.obs.index) & set(scab_dict.keys())
        common_ids = sorted(common_ids)
        self.cell_ids = common_ids  # Store cell_ids

        # Load input embeddings
        sliced_input = input_ad[common_ids]
        if issparse(sliced_input.X):
            self.input_embeddings = torch.tensor(sliced_input.X.toarray(), dtype=torch.float32)
        else:
            self.input_embeddings = torch.tensor(sliced_input.X, dtype=torch.float32)

        # Load scA/B scores
        self.scab_scores = torch.tensor([scab_dict[id_] for id_ in common_ids], dtype=torch.float32)

        # Load target embeddings and reshape
        sliced_target = target_ad[common_ids]
        if issparse(sliced_target.X):
            target_emb = sliced_target.X.toarray()
        else:
            target_emb = sliced_target.X
        self.target_embeddings = torch.tensor(target_emb, dtype=torch.float32).reshape(len(common_ids), patch_size, 768)

        # For train/val: split
        if mode in ['train', 'val']:
            n = len(common_ids)
            train_size = int(0.8 * n)
            val_size = n - train_size
            full_indices = list(range(n))
            train_indices, val_indices = random_split(full_indices, [train_size, val_size])
            self.indices = train_indices.indices if mode == 'train' else val_indices.indices
        elif mode == 'test':
            # For test: use all
            self.indices = list(range(len(common_ids)))
        else:
            raise ValueError("Invalid mode: choose 'train', 'val', or 'test'")

    def __len__(self):
        return len(self.indices)

    def __getitem__(self, idx):
        real_idx = self.indices[idx]
        cell_id = self.cell_ids[real_idx]  # Get cell_id
        return self.input_embeddings[real_idx], self.scab_scores[real_idx], self.target_embeddings[real_idx], cell_id

class ResBlock(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.fc1 = nn.Linear(dim, dim)
        self.fc2 = nn.Linear(dim, dim)
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.1)  # 新增: Dropout防过拟合

    def forward(self, x):
        res = x
        x = self.relu(self.norm1(self.fc1(x)))  # 加fc1
        x = self.dropout(x)  # Dropout
        x = self.norm2(self.fc2(x))
        x = self.dropout(x)  # Dropout
        return x + res

class MultiTaskModel(pl.LightningModule):
    def __init__(self, patch_size, scA_B_dim, model_path, input_dim=768, hidden_dim=1024, output_emb_dim=768, lr=1e-3):
        super().__init__()
        self.save_hyperparameters()
        self.shared = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            ResBlock(hidden_dim),
            ResBlock(hidden_dim),
            ResBlock(hidden_dim),
            ResBlock(hidden_dim)
        )

        self.head1 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Linear(hidden_dim * 2, scA_B_dim),
            nn.Sigmoid()
        )


        self.head2 = nn.Sequential(
            nn.Linear(hidden_dim, 512 * 4),  # 先投影到初始形状 (batch, 512*3)，用于 reshape 到 (batch, 512, 3)
            nn.ReLU()
        )
        self.deconv_layers = nn.ModuleList([
            nn.ConvTranspose1d(512, 256, kernel_size=4, stride=2, padding=1),  # 上采样层1
            nn.BatchNorm1d(256),
            nn.LeakyReLU(0.2),
            nn.ConvTranspose1d(256, 128, kernel_size=4, stride=2, padding=1),  # 上采样层2
            nn.BatchNorm1d(128),
            nn.LeakyReLU(0.2),
            nn.ConvTranspose1d(128, 64, kernel_size=4, stride=2, padding=1),   # 上采样层3
            nn.BatchNorm1d(64),
            nn.LeakyReLU(0.2),
            nn.ConvTranspose1d(64, output_emb_dim, kernel_size=5, stride=3, padding=2, output_padding=1)  # 最终上采样层，调整参数到长度95
        ])

        self.patch_size = patch_size
        self.output_emb_dim = output_emb_dim
        self.lr = lr
        self.scgraphic = scGrapHiC.load_from_checkpoint(model_path)
        self.scgraphic.freeze()  # 冻结以防梯度干扰

        # For test: collect outputs and cell_ids
        self.test_out1 = []
        self.test_out2 = []
        self.test_cell_ids = []

    def forward(self, x):
        feat = self.shared(x)
        out1 = self.head1(feat)
        out2 = self.head2(feat).reshape(-1, 512, 4)
        for layer in self.deconv_layers:
            out2 = layer(out2)
        out2 = torch.sigmoid(out2.transpose(1, 2))

        return out1, out2

    def _common_step(self, batch, prefix):
        x, y1, y2, _ = batch  # Ignore cell_ids in common steps
        out1, out2 = self(x)
        decoded_y2 = self._decode_embedding_to_hic_flat(y2)
        decoded_out2 = self._decode_embedding_to_hic_flat(out2)
        # 在 Module3_model.py 的 _common_step 中
        loss1_mse = F.mse_loss(out1, y1)
        #loss1_pearson = pearson_loss(out1, y1)  # 新增Pearson损失
        #loss1 = loss1_mse + 0.5 * loss1_pearson  # 组合
        loss1 = loss1_mse

        loss2 = F.mse_loss(out2, y2)
        loss3_mse = F.mse_loss(decoded_out2, decoded_y2)
        loss3_scc = scc_loss(decoded_out2, decoded_y2, self.patch_size)  # 新增SCC损失
        loss3 = loss3_mse + 0.5 * loss3_scc


        loss = loss1 + loss2 + loss3
        self.log_dict({
            f'{prefix}_loss': loss,
            f'{prefix}_loss1': loss1,
            f'{prefix}_loss2': loss2,
            f'{prefix}_loss3': loss3,
        }, on_epoch=True, prog_bar=True)

        return loss
    
    def _decode_embedding_to_hic_flat(self, embedding):
        # embedding: (batch, num_patches, final_embedding) -> (batch, num_patches * num_nodes**2)
        return self.scgraphic.transform_embedding_to_matrix(embedding.reshape(embedding.size(0), -1))

    def training_step(self, batch, batch_idx):
        return self._common_step(batch, 'train')

    def validation_step(self, batch, batch_idx):
        self._common_step(batch, 'val')

    def test_step(self, batch, batch_idx):
        x, y1, y2, cell_ids = batch  # Now batch includes cell_ids (list of strings)
        out1, out2 = self(x)
        decoded_y2 = self._decode_embedding_to_hic_flat(y2)
        decoded_out2 = self._decode_embedding_to_hic_flat(out2)
        loss1 = F.mse_loss(out1, y1)
        loss2 = F.mse_loss(out2, y2)
        loss3 = F.mse_loss(decoded_out2, decoded_y2)
        loss = loss1 + loss2 + loss3
        self.log_dict({
            'test_loss': loss,
            'test_loss1': loss1,
            'test_loss2': loss2,
            'test_loss3': loss3,
        }, on_epoch=True, prog_bar=True)

        # Collect for on_test_end
        self.test_out1.append(out1.detach().cpu())
        self.test_out2.append(out2.detach().cpu())
        self.test_cell_ids.extend(cell_ids)  # Extend list of cell_ids
        
        return loss

    def on_test_end(self):
        # Concat all batches
        if self.test_out1:  
            out1_all = torch.cat(self.test_out1, dim=0).numpy()  # (total_cells, scA_B_dim)
            out2_all = torch.cat(self.test_out2, dim=0).numpy()  # (total_cells, patch_size, 768)
            cell_ids = self.test_cell_ids  # list of str

            # Save out1 as CSV table: reconstruct similar to target_csv
            # Assume original target_csv has chrom, bin as first two columns, then cell columns
            # So, out1_df: first two columns from target_df_prefix, then columns as cell_ids with out1_all.T
            #target_df_prefix = self.trainer.datamodule.test_dataset.target_df_prefix  # Assume accessible, or load again if needed
            out1_df = pd.DataFrame(out1_all.T, columns=cell_ids)  # (scA_B_dim, total_cells)
            #out1_df = pd.concat([target_df_prefix, out1_df], axis=1)
            out1_df.to_csv('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\predicted_scAB.csv', index=False)

            # Save out2 as AnnData h5ad: X = out2_all.reshape(total_cells, patch_size * 768), obs with cell_id
            adata = ad.AnnData(X=out2_all.reshape(len(cell_ids), -1))
            adata.obs['cell_id'] = cell_ids
            adata.obs.set_index('cell_id', inplace=True)
            adata.write('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\predicted_embeddings.h5ad')

            # Clear lists
            self.test_out1 = []
            self.test_out2 = []
            self.test_cell_ids = []
        
        print("Test results saved to 'predicted_scAB.csv' and 'predicted_embeddings.h5ad'")

    def configure_optimizers(self):
        # 使用 AdamW 并带有 weight_decay
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.lr,
            weight_decay=1e-4
        )
        # 使用 ReduceLROnPlateau 调度器，监听验证集损失
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',            # 监控指标是否越小越好
            factor=0.1,            # 学习率调整倍数（0.1 表示降为原来的 1/10）
            patience=1,            # 容忍指标不下降的 epoch 数
            min_lr=1e-6,           # 学习率下限
            verbose=True           # 是否打印调度信息
        )
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'interval': 'epoch',
                'monitor': 'val_loss',  # 监听训练集 Loss
            }
        }
