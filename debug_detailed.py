import torch
import torch.nn as nn
import os
from torch.utils.data import DataLoader
import lightning.pytorch as pl
from Module3_model_optimized import MultiTaskModelOptimized, MultiTaskDataset
from Module3_utils import initialize_parameters_from_args
import warnings
warnings.filterwarnings('ignore')

def set_seed(seed):
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    print(f"Seed set to {seed}")

def debug_step_by_step():
    print("=== Step 1: Parameter initialization ===")
    try:
        PARAMETERS = initialize_parameters_from_args()
        print(f"Parameters loaded successfully")
        print(f"Key parameters: input_dim={PARAMETERS.get('input_dim')}, batch_size={PARAMETERS.get('batch_size')}")
    except Exception as e:
        print(f"Error in parameter initialization: {e}")
        return
    
    print("\n=== Step 2: Seed setting ===")
    try:
        pl.seed_everything(PARAMETERS['seed'])
        print(f"Seed set to {PARAMETERS['seed']}")
    except Exception as e:
        print(f"Error setting seed: {e}")
        return
    
    print("\n=== Step 3: Dataset creation ===")
    try:
        print("Creating train dataset...")
        train_dataset = MultiTaskDataset(
            input_path=PARAMETERS['input_h5ad'],
            target_csv_path=PARAMETERS['target_csv'],
            target_h5ad_path=PARAMETERS['target_h5ad'],
            patch_size=PARAMETERS['patch_size'],
            mode='train'
        )
        print(f"Train dataset created: {len(train_dataset)} samples")
        
        print("Creating validation dataset...")
        val_dataset = MultiTaskDataset(
            input_path=PARAMETERS['input_h5ad'],
            target_csv_path=PARAMETERS['target_csv'],
            target_h5ad_path=PARAMETERS['target_h5ad'],
            patch_size=PARAMETERS['patch_size'],
            mode='val'
        )
        print(f"Validation dataset created: {len(val_dataset)} samples")
    except Exception as e:
        print(f"Error creating datasets: {e}")
        return
    
    print("\n=== Step 4: DataLoader creation ===")
    try:
        print("Creating train dataloader...")
        train_loader = torch.utils.data.DataLoader(
            train_dataset, 
            batch_size=PARAMETERS['batch_size'], 
            shuffle=True, 
            num_workers=0
        )
        print(f"Train dataloader created")
        
        print("Creating validation dataloader...")
        val_loader = torch.utils.data.DataLoader(
            val_dataset, 
            batch_size=PARAMETERS['batch_size'], 
            shuffle=False, 
            num_workers=0
        )
        print(f"Validation dataloader created")
    except Exception as e:
        print(f"Error creating dataloaders: {e}")
        return
    
    print("\n=== Step 5: Testing data loading ===")
    try:
        print("Getting first batch from train loader...")
        first_batch = next(iter(train_loader))
        print(f"First batch loaded successfully")
        print(f"Batch type: {type(first_batch)}")
        if isinstance(first_batch, (list, tuple)):
            print(f"Batch length: {len(first_batch)}")
            for i, item in enumerate(first_batch):
                if hasattr(item, 'shape'):
                    print(f"  Item {i}: shape {item.shape}")
                else:
                    print(f"  Item {i}: {type(item)}")
        else:
            print(f"Unexpected batch format: {first_batch}")
    except Exception as e:
        print(f"Error loading first batch: {e}")
        return
    
    print("\n=== Step 6: Model creation ===")
    try:
        print("Creating model...")
        # Check if checkpoint exists
        import os
        checkpoint_path = "c:/Users/<USER>/Desktop/调整参数/数据1/数据1/epoch=99-step=5400.ckpt"
        if os.path.exists(checkpoint_path):
            print(f"Using checkpoint: {checkpoint_path}")
            model_path = checkpoint_path
        else:
            print("No checkpoint found, this will cause an error in model creation")
            print("Available files in data directory:")
            data_dir = "c:/Users/<USER>/Desktop/调整参数/数据1/数据1"
            if os.path.exists(data_dir):
                for f in os.listdir(data_dir):
                    print(f"  {f}")
            return
        
        model = MultiTaskModelOptimized(
            patch_size=PARAMETERS['patch_size'],
            scA_B_dim=PARAMETERS['output_dim1'],
            model_path=model_path,
            input_dim=PARAMETERS['input_dim'],
            hidden_dim=PARAMETERS['hidden_dim'],
            output_emb_dim=PARAMETERS['output_emb_dim'],
            lr=PARAMETERS['learning_rate']
        )
        print("Model created successfully")
        print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")
    except Exception as e:
        print(f"Error creating model: {e}")
        return
    
    print("\n=== All steps completed successfully! ===")

if __name__ == '__main__':
    debug_step_by_step()