import torch
import lightning.pytorch as pl
from Module3_model_optimized import MultiTaskModelOptimized, MultiTaskDataset
from Module3_utils import initialize_parameters_from_args
import warnings
import os
from lightning.pytorch.loggers import TensorBoardLogger
from lightning.pytorch.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor

# 过滤警告
warnings.filterwarnings('ignore')

def set_seed(seed):
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    print(f"Seed set to {seed}")

def main():
    print("=== 步骤1: 初始化参数 ===")
    try:
        PARAMETERS = initialize_parameters_from_args()
        print("参数初始化成功")
        print(f"关键参数: batch_size={PARAMETERS['batch_size']}, learning_rate={PARAMETERS['learning_rate']}")
    except Exception as e:
        print(f"参数初始化失败: {e}")
        return
    
    print("\n=== 步骤2: 设置随机种子 ===")
    try:
        set_seed(42)
        print("随机种子设置成功")
    except Exception as e:
        print(f"随机种子设置失败: {e}")
        return
    
    print("\n=== 步骤3: 创建数据集 ===")
    try:
        train_dataset = MultiTaskDataset(
            input_path='数据1/数据1/scRNA_embedding_train.h5ad',
            target_csv_path='数据1/数据1/with_scRNA_30000_cutoff_scA_B.csv',
            target_h5ad_path='数据1/数据1/scRNA_embedding_train.h5ad',
            patch_size=95,
            mode='train'
        )
        print(f"训练数据集创建成功，样本数: {len(train_dataset)}")
        
        val_dataset = MultiTaskDataset(
            input_path='数据1/数据1/scRNA_embedding_train.h5ad',
            target_csv_path='数据1/数据1/with_scRNA_30000_cutoff_scA_B.csv',
            target_h5ad_path='数据1/数据1/scRNA_embedding_train.h5ad',
            patch_size=95,
            mode='val'
        )
        print(f"验证数据集创建成功，样本数: {len(val_dataset)}")
    except Exception as e:
        print(f"数据集创建失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n=== 步骤4: 创建数据加载器 ===")
    try:
        train_loader = torch.utils.data.DataLoader(
            train_dataset, 
            batch_size=PARAMETERS['batch_size'], 
            shuffle=True, 
            num_workers=0,
            pin_memory=True if torch.cuda.is_available() else False
        )
        print("训练数据加载器创建成功")
        
        val_loader = torch.utils.data.DataLoader(
            val_dataset, 
            batch_size=PARAMETERS['batch_size'], 
            shuffle=False, 
            num_workers=0,
            pin_memory=True if torch.cuda.is_available() else False
        )
        print("验证数据加载器创建成功")
    except Exception as e:
        print(f"数据加载器创建失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n=== 步骤5: 测试数据加载 ===")
    try:
        print("正在获取第一个训练批次...")
        train_batch = next(iter(train_loader))
        print(f"训练批次获取成功，类型: {type(train_batch)}, 长度: {len(train_batch)}")
        for i, item in enumerate(train_batch):
            if hasattr(item, 'shape'):
                print(f"  元素{i}: shape={item.shape}, dtype={item.dtype}")
            else:
                print(f"  元素{i}: type={type(item)}, len={len(item) if hasattr(item, '__len__') else 'N/A'}")
    except Exception as e:
        print(f"数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n=== 步骤6: 创建模型 ===")
    try:
        checkpoint_path = '数据1/数据1/epoch=99-step=5400.ckpt'
        if os.path.exists(checkpoint_path):
            print(f"找到检查点文件: {checkpoint_path}")
            model_path = checkpoint_path
        else:
            print(f"检查点文件不存在: {checkpoint_path}")
            print("当前目录文件:")
            for root, dirs, files in os.walk('.'):
                for file in files:
                    if file.endswith('.ckpt'):
                        print(f"  {os.path.join(root, file)}")
            return
        
        print("正在创建模型...")
        model = MultiTaskModelOptimized(
            patch_size=PARAMETERS['patch_size'],
            scA_B_dim=PARAMETERS['scA_B_dim'],
            model_path=model_path,
            input_dim=PARAMETERS['input_dim'],
            hidden_dim=PARAMETERS['hidden_dim'],
            output_emb_dim=PARAMETERS['output_emb_dim'],
            lr=PARAMETERS['learning_rate']
        )
        print("模型创建成功")
    except Exception as e:
        print(f"模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n=== 步骤7: 创建日志记录器 ===")
    try:
        # 禁用TensorBoard日志记录器
        tb_logger = None
        print("日志记录器已禁用")
    except Exception as e:
        print(f"日志记录器创建失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n=== 步骤8: 创建回调函数 ===")
    try:
        callbacks = [
            ModelCheckpoint(
                monitor='val_loss3',
                mode='min',
                save_top_k=3,
                filename='best-{epoch:02d}-{val_loss3:.4f}',
                save_last=True
            ),
            EarlyStopping(
                monitor='val_loss3',
                patience=15,
                mode='min',
                verbose=True
            )
        ]
        print("回调函数创建成功")
    except Exception as e:
        print(f"回调函数创建失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n=== 步骤9: 创建训练器 ===")
    try:
        trainer = pl.Trainer(
            max_epochs=PARAMETERS['epochs'],
            logger=False,
            callbacks=callbacks,
            gradient_clip_val=PARAMETERS['gradient_clip_value'],
            accelerator='gpu' if torch.cuda.is_available() else 'cpu',
            devices=1,
            precision='16-mixed' if torch.cuda.is_available() else 32,
            log_every_n_steps=10,
            val_check_interval=0.5,
            accumulate_grad_batches=2,
            enable_progress_bar=True,
            enable_model_summary=True
        )
        print("训练器创建成功")
    except Exception as e:
        print(f"训练器创建失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n=== 所有步骤完成，准备开始训练 ===")
    print(f"训练样本数: {len(train_dataset)}")
    print(f"验证样本数: {len(val_dataset)}")
    print(f"批次大小: {PARAMETERS['batch_size']}")
    print(f"学习率: {PARAMETERS['learning_rate']}")
    print(f"设备: {'GPU' if torch.cuda.is_available() else 'CPU'}")
    
    print("\n开始训练...")
    try:
        trainer.fit(model, train_loader, val_loader)
        print("训练完成！")
    except Exception as e:
        print(f"训练过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()