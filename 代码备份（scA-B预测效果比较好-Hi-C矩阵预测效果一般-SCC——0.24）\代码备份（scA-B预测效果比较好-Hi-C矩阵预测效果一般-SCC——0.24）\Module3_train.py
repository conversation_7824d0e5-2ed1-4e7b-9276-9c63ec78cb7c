from Module3_model import *
from Module3_utils import *
import gc
import os
import anndata
import pandas as pd
import torch
import lightning.pytorch as pl
#warnings.filterwarnings('ignore', category=RuntimeWarning)
from lightning.pytorch.strategies import DDPStrategy
from lightning.pytorch.loggers import TensorBoardLogger
from lightning.pytorch.callbacks import ModelCheckpoint
from lightning.pytorch.callbacks import EarlyStopping
from torch.utils.data import Dataset, DataLoader, random_split

PARAMETERS = initialize_parameters_from_args()
pl.seed_everything(PARAMETERS['seed'])

train_ds = MultiTaskDataset(PARAMETERS['input_h5ad'], PARAMETERS['target_csv'], PARAMETERS['target_h5ad'], PARAMETERS['patch_size'], mode='train')
val_ds = MultiTaskDataset(PARAMETERS['input_h5ad'], PARAMETERS['target_csv'], PARAMETERS['target_h5ad'], PARAMETERS['patch_size'], mode='val')

train_loader = DataLoader(train_ds, batch_size=PARAMETERS['batch_size'], num_workers=63, shuffle=True)
val_loader = DataLoader(val_ds, batch_size=PARAMETERS['batch_size'], num_workers=63)
model = MultiTaskModel(patch_size=PARAMETERS['patch_size'], 
                       scA_B_dim=PARAMETERS['scA_B_dim'], 
                       model_path='c:\\Users\\<USER>\\Desktop\\调整参数\\数据1\\数据1\\epoch=99-step=5400.ckpt', 
                       input_dim=768, 
                       hidden_dim=1024, 
                       output_emb_dim=768, 
                       lr=PARAMETERS['lr'])

tb_logger = TensorBoardLogger('c:\\Users\\<USER>\\Desktop\\调整参数\\result', name='Module3_result')
checkpoint_callback = ModelCheckpoint(monitor="val_loss",  save_top_k=1, mode='min')
# 创建早停回调函数
early_stop_callback = EarlyStopping(
    monitor='val_loss',  # 监控的验证指标
    min_delta=0.00,  # 最小改进量，如果改进小于这个值，则认为没有改进
    patience=3,  # 容忍没有改进的轮数
    verbose=True,  # 是否打印早停信息
    mode='min'  # 监控指标是应该被最小化还是最大化
)




trainer = pl.Trainer(
    max_epochs=PARAMETERS['epochs'],
    check_val_every_n_epoch=1,
    logger=tb_logger,
    deterministic=True,
    callbacks=[checkpoint_callback, early_stop_callback],
    gradient_clip_val=PARAMETERS['gradient_clip_value'],
    accelerator="gpu",
    devices="auto",  # 自动检测所有可用GPU
    strategy=DDPStrategy(find_unused_parameters=True)  # 使用分布式数据并行 (DDP)
)



trainer.fit(model, train_loader, val_loader)
