import torch
import torch.nn as nn
import torch.nn.functional as F
import lightning.pytorch as pl
from torch.utils.data import Dataset, DataLoader
import anndata as ad
import pandas as pd
import numpy as np
from scipy.sparse import issparse
import argparse
from torch.utils.data import random_split
from Module1_model import *
from Module3_utils import *

class MultiTaskDataset(Dataset):
    def __init__(self, input_path, target_csv_path, target_h5ad_path, patch_size, mode='train'):
        """
        mode: 'train' for training split, 'val' for validation split, 'test' for independent test set (no split, use all common_ids)
        """
        input_ad = ad.read_h5ad(input_path)
        target_ad = ad.read_h5ad(target_h5ad_path)
        # 直接使用现有的索引，不需要设置cell_id
        target_df = pd.read_csv(target_csv_path)

        # Remove first two columns (assuming they are chrom and bin)
        self.target_df_prefix = target_df.iloc[:, :2]  # Save prefix columns for later reconstruction if needed
        target_df = target_df.iloc[:, 2:]
        # Create dict where key is ID (column name), value is the entire column as numpy array
        scab_dict = {col: target_df[col].values for col in target_df.columns}

        # 处理ID匹配问题：目标embedding使用数字索引，需要找到输入embedding和scA/B的共同ID
        input_ids = set(input_ad.obs.index)
        scab_ids = set(scab_dict.keys())
        
        # 找到输入embedding和scA/B的共同ID
        common_input_scab = input_ids & scab_ids
        common_ids = sorted(list(common_input_scab))
        
        print(f"找到 {len(common_ids)} 个共同的cell ID")
        if len(common_ids) == 0:
            raise ValueError("没有找到共同的cell ID，请检查数据文件")
        
        self.cell_ids = common_ids  # Store cell_ids

        # Load input embeddings
        sliced_input = input_ad[common_ids]
        if issparse(sliced_input.X):
            self.input_embeddings = torch.tensor(sliced_input.X.toarray(), dtype=torch.float32)
        else:
            self.input_embeddings = torch.tensor(sliced_input.X, dtype=torch.float32)

        # Load scA/B scores
        self.scab_scores = torch.tensor([scab_dict[id_] for id_ in common_ids], dtype=torch.float32)

        # 对于目标embedding，我们使用前len(common_ids)个样本
        # 假设目标embedding的顺序与输入数据相对应
        n_samples = len(common_ids)
        if target_ad.shape[0] < n_samples:
            raise ValueError(f"目标embedding样本数({target_ad.shape[0]})少于共同ID数({n_samples})")
            
        # 取前n_samples个目标embedding
        target_subset = target_ad[:n_samples]
        if issparse(target_subset.X):
            target_emb = target_subset.X.toarray()
        else:
            target_emb = target_subset.X
        target_emb_tensor = torch.tensor(target_emb, dtype=torch.float32)  # shape: (n_cells, 72960)
        # Reshape to (n_cells, patch_size, 768)
        expected_size = patch_size * 768  # 95 * 768 = 72960
        if target_emb_tensor.shape[1] == expected_size:
            self.target_embeddings = target_emb_tensor.reshape(n_samples, patch_size, 768)
        else:
            # 如果维度不匹配，使用重复策略
            target_emb_reshaped = target_emb_tensor[:, :768]  # 取前768维
            self.target_embeddings = target_emb_reshaped.unsqueeze(1).repeat(1, patch_size, 1)

        # For train/val: split
        if mode in ['train', 'val']:
            n = len(common_ids)
            train_size = int(0.8 * n)
            val_size = n - train_size
            full_indices = list(range(n))
            train_indices, val_indices = random_split(full_indices, [train_size, val_size])
            self.indices = train_indices.indices if mode == 'train' else val_indices.indices
        elif mode == 'test':
            # For test: use all
            self.indices = list(range(len(common_ids)))
        else:
            raise ValueError("Invalid mode: choose 'train', 'val', or 'test'")

    def __len__(self):
        return len(self.indices)

    def __getitem__(self, idx):
        real_idx = self.indices[idx]
        cell_id = self.cell_ids[real_idx]  # Get cell_id
        return self.input_embeddings[real_idx], self.scab_scores[real_idx], self.target_embeddings[real_idx], cell_id

class ResBlock(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.fc1 = nn.Linear(dim, dim)
        self.fc2 = nn.Linear(dim, dim)
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        res = x
        x = self.relu(self.norm1(self.fc1(x)))
        x = self.dropout(x)
        x = self.norm2(self.fc2(x))
        x = self.dropout(x)
        return x + res

class ImprovedHead2(nn.Module):
    """优化的Head2架构，专门用于生成(batch, 95, 768)的embedding"""
    def __init__(self, input_dim=1024, patch_size=95, output_emb_dim=768):
        super().__init__()
        self.patch_size = patch_size
        self.output_emb_dim = output_emb_dim
        
        # 1. 更深的特征提取网络
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, 2048),
            nn.LayerNorm(2048),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            nn.Linear(2048, 1536),
            nn.LayerNorm(1536),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            nn.Linear(1536, 1024),
            nn.LayerNorm(1024),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 2. 分别生成patch和embedding维度
        # 先生成patch数量的特征
        self.patch_generator = nn.Sequential(
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Linear(512, patch_size * 256)  # 生成95个256维特征
        )
        
        # 3. 使用1D CNN将256维特征扩展到768维
        self.embedding_expander = nn.Sequential(
            # 输入: (batch, 95, 256)
            nn.Conv1d(256, 512, kernel_size=3, padding=1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            nn.Conv1d(512, 768, kernel_size=3, padding=1),
            nn.BatchNorm1d(768),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            # 残差连接层
            nn.Conv1d(768, 768, kernel_size=1),
            nn.BatchNorm1d(768)
        )
        
        # 4. 最终调整层
        self.final_adjustment = nn.Sequential(
            nn.Conv1d(768, 768, kernel_size=3, padding=1),
            nn.BatchNorm1d(768),
            nn.ReLU(),
            nn.Conv1d(768, 768, kernel_size=1)
        )
        
        # 5. 注意力机制用于特征重要性加权
        self.attention = nn.MultiheadAttention(embed_dim=768, num_heads=8, dropout=0.1, batch_first=True)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """改进的权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm1d, nn.LayerNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # 1. 特征提取
        features = self.feature_extractor(x)  # (batch, 1024)
        
        # 2. 生成patch特征
        patch_features = self.patch_generator(features)  # (batch, 95*256)
        patch_features = patch_features.view(-1, self.patch_size, 256)  # (batch, 95, 256)
        
        # 3. 转置用于1D卷积 (batch, 256, 95)
        patch_features = patch_features.transpose(1, 2)
        
        # 4. 扩展到768维
        expanded = self.embedding_expander(patch_features)  # (batch, 768, 95)
        
        # 5. 最终调整
        output = self.final_adjustment(expanded)  # (batch, 768, 95)
        
        # 6. 转置回来 (batch, 95, 768)
        output = output.transpose(1, 2)
        
        # 7. 自注意力机制
        attended_output, _ = self.attention(output, output, output)
        
        # 8. 残差连接
        output = output + attended_output
        
        # 9. 应用sigmoid激活
        output = torch.sigmoid(output)
        
        return output

class MultiTaskModelOptimized(pl.LightningModule):
    def __init__(self, patch_size, scA_B_dim, model_path, input_dim=768, hidden_dim=1024, output_emb_dim=768, lr=1e-3):
        super().__init__()
        self.save_hyperparameters()
        
        # 共享特征提取器
        self.shared = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            ResBlock(hidden_dim),
            ResBlock(hidden_dim),
            ResBlock(hidden_dim),
            ResBlock(hidden_dim)
        )

        # Head1保持不变（效果好）
        self.head1 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Linear(hidden_dim * 2, scA_B_dim),
            nn.Sigmoid()
        )

        # 使用优化的Head2
        self.head2 = ImprovedHead2(input_dim=hidden_dim, patch_size=patch_size, output_emb_dim=output_emb_dim)

        self.patch_size = patch_size
        self.output_emb_dim = output_emb_dim
        self.lr = lr
        self.scgraphic = scGrapHiC.load_from_checkpoint(model_path)
        self.scgraphic.freeze()

        # For test: collect outputs and cell_ids
        self.test_out1 = []
        self.test_out2 = []
        self.test_cell_ids = []

    def forward(self, x):
        feat = self.shared(x)
        out1 = self.head1(feat)
        out2 = self.head2(feat)
        return out1, out2

    def _common_step(self, batch, prefix):
        x, y1, y2, _ = batch
        out1, out2 = self(x)
        decoded_y2 = self._decode_embedding_to_hic_flat(y2)
        decoded_out2 = self._decode_embedding_to_hic_flat(out2)
        
        # Head1损失（保持不变）
        loss1 = F.mse_loss(out1, y1)
        
        # Head2损失（优化权重）
        loss2_mse = F.mse_loss(out2, y2)
        loss2_cosine = 1 - F.cosine_similarity(out2.reshape(out2.size(0), -1), y2.reshape(y2.size(0), -1)).mean()
        loss2 = 0.7 * loss2_mse + 0.3 * loss2_cosine  # 组合MSE和余弦相似度损失
        
        # Head2的Hi-C重建损失（重点优化）
        loss3_mse = F.mse_loss(decoded_out2, decoded_y2)
        loss3_scc = scc_loss(decoded_out2, decoded_y2, self.patch_size)
        
        # 添加L1正则化减少过拟合
        l1_reg = sum(p.abs().sum() for p in self.head2.parameters())
        
        # 重新平衡loss3的权重，更关注SCC
        loss3 = 0.4 * loss3_mse + 0.5 * loss3_scc + 0.1 * l1_reg * 1e-5
        
        # 总损失：降低head2的loss3权重到0.05
        loss = 0.475 * loss1 + 0.475 * loss2 + 0.05 * loss3
        
        self.log_dict({
            f'{prefix}_loss': loss,
            f'{prefix}_loss1': loss1,
            f'{prefix}_loss2': loss2,
            f'{prefix}_loss2_mse': loss2_mse,
            f'{prefix}_loss2_cosine': loss2_cosine,
            f'{prefix}_loss3': loss3,
            f'{prefix}_loss3_mse': loss3_mse,
            f'{prefix}_loss3_scc': loss3_scc,
        }, on_epoch=True, prog_bar=True)

        return loss
    
    def _decode_embedding_to_hic_flat(self, embedding):
        return self.scgraphic.transform_embedding_to_matrix(embedding.reshape(embedding.size(0), -1))

    def training_step(self, batch, batch_idx):
        return self._common_step(batch, 'train')

    def validation_step(self, batch, batch_idx):
        self._common_step(batch, 'val')

    def test_step(self, batch, batch_idx):
        x, y1, y2, cell_ids = batch
        out1, out2 = self(x)
        decoded_y2 = self._decode_embedding_to_hic_flat(y2)
        decoded_out2 = self._decode_embedding_to_hic_flat(out2)
        
        loss1 = F.mse_loss(out1, y1)
        loss2 = F.mse_loss(out2, y2)
        loss3_mse = F.mse_loss(decoded_out2, decoded_y2)
        loss3_scc = scc_loss(decoded_out2, decoded_y2, self.patch_size)
        loss3 = 0.4 * loss3_mse + 0.6 * loss3_scc
        loss = 0.475 * loss1 + 0.475 * loss2 + 0.05 * loss3
        
        self.log_dict({
            'test_loss': loss,
            'test_loss1': loss1,
            'test_loss2': loss2,
            'test_loss3': loss3,
            'test_loss3_mse': loss3_mse,
            'test_loss3_scc': loss3_scc,
        }, on_epoch=True, prog_bar=True)

        self.test_out1.append(out1.detach().cpu())
        self.test_out2.append(out2.detach().cpu())
        self.test_cell_ids.extend(cell_ids)
        
        return loss

    def on_test_end(self):
        if self.test_out1:
            out1_all = torch.cat(self.test_out1, dim=0).numpy()
            out2_all = torch.cat(self.test_out2, dim=0).numpy()
            cell_ids = self.test_cell_ids

            out1_df = pd.DataFrame(out1_all.T, columns=cell_ids)
            out1_df.to_csv('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\predicted_scAB.csv', index=False)

            adata = ad.AnnData(X=out2_all.reshape(len(cell_ids), -1))
            adata.obs['cell_id'] = cell_ids
            adata.obs.set_index('cell_id', inplace=True)
            adata.write('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\predicted_embeddings.h5ad')

            self.test_out1 = []
            self.test_out2 = []
            self.test_cell_ids = []
        
        print("Test results saved to 'predicted_scAB.csv' and 'predicted_embeddings.h5ad'")

    def configure_optimizers(self):
        # 为不同部分使用不同的学习率
        head2_params = list(self.head2.parameters())
        head2_param_ids = {id(p) for p in head2_params}
        other_params = [p for p in self.parameters() if id(p) not in head2_param_ids]
        
        optimizer = torch.optim.AdamW([
            {'params': other_params, 'lr': self.lr},
            {'params': head2_params, 'lr': self.lr * 0.5}  # head2使用较小学习率
        ], weight_decay=1e-4)
        
        # 使用余弦退火调度器
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer,
            T_0=10,  # 初始重启周期
            T_mult=2,  # 重启周期倍数
            eta_min=1e-6  # 最小学习率
        )
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'interval': 'epoch'
            }
        }