# Module3_infer.py
from Module3_model import *
from Module3_utils import *
import gc
import os
import anndata
import pandas as pd
import torch
import lightning.pytorch as pl
#warnings.filterwarnings('ignore', category=RuntimeWarning)
from lightning.pytorch.strategies import DDPStrategy
from lightning.pytorch.loggers import TensorBoardLogger
from lightning.pytorch.callbacks import ModelCheckpoint
from lightning.pytorch.callbacks import EarlyStopping
from torch.utils.data import Dataset, DataLoader, random_split

from scipy.spatial.distance import cosine
from scipy.stats import pearsonr
import numpy as np

PARAMETERS = initialize_parameters_from_args()
pl.seed_everything(PARAMETERS['seed'])
DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'


# Load dataset for test (mode='test')
test_ds = MultiTaskDataset(
    PARAMETERS['input_h5ad'], 
    PARAMETERS['target_csv'], 
    PARAMETERS['target_h5ad'], 
    PARAMETERS['patch_size'], 
    mode='test'
)
test_loader = DataLoader(test_ds, batch_size=PARAMETERS['batch_size'], shuffle=False)

# Load model from checkpoint
model = MultiTaskModel.load_from_checkpoint('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\version_21\\checkpoints\\epoch=12-step=754.ckpt')
model = model.to(DEVICE)
# Trainer for inference (test)
tb_logger = TensorBoardLogger('c:\\Users\\<USER>\\Desktop\\调整参数\\result', name='Module3_result')
checkpoint_callback = ModelCheckpoint(monitor="val_loss",  save_top_k=1, mode='min')
# 创建早停回调函数
early_stop_callback = EarlyStopping(
    monitor='val_loss',  # 监控的验证指标
    min_delta=0.00,  # 最小改进量，如果改进小于这个值，则认为没有改进
    patience=3,  # 容忍没有改进的轮数
    verbose=True,  # 是否打印早停信息
    mode='min'  # 监控指标是应该被最小化还是最大化
)




trainer = pl.Trainer(
    max_epochs=PARAMETERS['epochs'],
    check_val_every_n_epoch=1,
    logger=tb_logger,
    deterministic=True,
    callbacks=[checkpoint_callback, early_stop_callback],
    gradient_clip_val=PARAMETERS['gradient_clip_value'],
    accelerator="gpu",
    devices="1",  # 自动检测所有可用GPU
    strategy=DDPStrategy(find_unused_parameters=True)  # 使用分布式数据并行 (DDP)
)

# Run test (which will predict and save out1 as CSV, out2 as h5ad)
trainer.test(model, dataloaders=test_loader)

# After saving, load predicted_scAB.csv and target_csv to compute Pearson per cell
pred_scAB = pd.read_csv('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\predicted_scAB.csv')
target_scAB = pd.read_csv(PARAMETERS['target_csv'])

# Assume both have same structure: first two columns chrom/bin, then cell_id columns
cell_columns = pred_scAB.columns  # Cell ids
pearson_scores = {}

results = []

for cell_id in cell_columns:
    pred_vec = pred_scAB[cell_id].values.astype(float)
    target_vec = target_scAB[cell_id].values.astype(float)

    # Pearson
    pearson_corr, _ = pearsonr(pred_vec, target_vec)

    # Cosine similarity
    # 注意：scipy cosine distance = 1 - cosine similarity
    cosine_sim = 1 - cosine(pred_vec, target_vec)

    # Jensen-Shannon Divergence
    # 归一化到概率分布
    p = pred_vec / np.sum(pred_vec + 1e-12)
    q = target_vec / np.sum(target_vec + 1e-12)
    m = 0.5 * (p + q)
    kl_pm = np.sum(p * np.log((p + 1e-12) / (m + 1e-12)))
    kl_qm = np.sum(q * np.log((q + 1e-12) / (m + 1e-12)))
    jsd = 0.5 * (kl_pm + kl_qm)

    results.append({
        'cell_id': cell_id,
        'Pearson_Correlation': pearson_corr,
        'Cosine_Similarity': cosine_sim,
        'Jensen_Shannon_Divergence': jsd
    })

# 保存结果
result_df = pd.DataFrame(results)
result_df.to_csv('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\metrics_per_cell.csv', index=False)

# 打印平均值
print("Average Pearson Correlation:", result_df['Pearson_Correlation'].mean())
print("Average Cosine Similarity:", result_df['Cosine_Similarity'].mean())
print("Average Jensen-Shannon Divergence:", result_df['Jensen_Shannon_Divergence'].mean())

import seaborn as sns
sns.histplot(pred_scAB[cell_id], label='Pred')
sns.histplot(target_scAB[cell_id], label='Target')
plt.savefig('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\dist_hist.pdf')

# Decode to Hi-C matrices (using utils)
scgraphic = scGrapHiC.load_from_checkpoint('c:\\Users\\<USER>\\Desktop\\调整参数\\数据1\\数据1\\epoch=99-step=5400.ckpt')
#scgraphic.PARAMETERS = PARAMETERS
scgraphic = scgraphic.to(DEVICE)





# 加载 metadata
df_metadatas = load_and_merge_data('c:\\Users\\<USER>\\Desktop\\调整参数\\数据1\\数据1\\HiRES_Mouse_377_1000000.pt')

# 加载预测 embedding
generated_embeddings = ad.read_h5ad('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\predicted_embeddings.h5ad')
#generated_embeddings.obs = generated_embeddings.obs.set_index('cell_id')
embedding_matrix = torch.from_numpy(generated_embeddings.X).float()

# 获取 generated_embeddings 中的 cell_id 列表（保持顺序）
generated_cell_ids = generated_embeddings.obs.index.tolist()

# 加载 target_embeddings
target_embeddings = ad.read_h5ad(PARAMETERS['target_h5ad'])
target_embeddings.obs = target_embeddings.obs.set_index('cell_id')

# 使用 .loc 按 generated_cell_ids 顺序提取，确保顺序完全一致
target_embeddings = target_embeddings[generated_cell_ids, :]

# 再次确认对齐
assert (target_embeddings.obs.index == generated_embeddings.obs.index).all(), "cell_id 顺序未对齐！"

# 提取对齐后的 tensor
target_embedding_matrix = torch.from_numpy(target_embeddings.X).float()

# 保存 SCC 结果
scc_results = []

# 处理前10个样本（可根据需要扩展）
for i in tqdm(range(0, 10), desc="Processing batches", unit="batch"):
    cell_id = generated_cell_ids[i]

    # === 处理 generated ===
    batch_gen = embedding_matrix[i].to(DEVICE)
    embedding_dim = batch_gen.shape[0]
    final_embedding = PARAMETERS['final_embedding']
    assert embedding_dim % final_embedding == 0
    num_segments = embedding_dim // final_embedding
    sub_vectors_gen = batch_gen.view(num_segments, final_embedding)

    decoded_gen = []
    batch_size_for_decoder = 16
    with torch.no_grad():
        for start in range(0, num_segments, batch_size_for_decoder):
            end = min(start + batch_size_for_decoder, num_segments)
            sub_batch = sub_vectors_gen[start:end].float()
            decoded_matrices = scgraphic.autoencode.decoder(sub_batch)
            decoded_gen.extend([m.cpu() for m in decoded_matrices])

    # === 处理 target ===
    batch_tar = target_embedding_matrix[i].to(DEVICE)
    sub_vectors_tar = batch_tar.view(num_segments, final_embedding)

    decoded_tar = []
    with torch.no_grad():
        for start in range(0, num_segments, batch_size_for_decoder):
            end = min(start + batch_size_for_decoder, num_segments)
            sub_batch = sub_vectors_tar[start:end].float()
            decoded_matrices = scgraphic.autoencode.decoder(sub_batch)
            decoded_tar.extend([m.cpu() for m in decoded_matrices])

    # === 遍历每个染色体 ===
    for z in df_metadatas['chr'].unique():
        df_chr = df_metadatas[df_metadatas['chr'] == z].reset_index(drop=True)

        # 构造 generated 矩阵
        array_gen = np.zeros((len(df_chr), PARAMETERS['stride'], PARAMETERS['num_nodes']))
        for j in range(len(df_chr)):
            array_gen[j] = decoded_gen[j][0]

        indices = df_chr[['chr', 'original_size', 'row_id', 'col_id']].to_numpy()
        mat_gen = reconstruct_matrix(array_gen, indices, PARAMETERS)

        # 构造 target 矩阵
        array_tar = np.zeros((len(df_chr), PARAMETERS['stride'], PARAMETERS['num_nodes']))
        for j in range(len(df_chr)):
            array_tar[j] = decoded_tar[j][0]

        mat_tar = reconstruct_matrix(array_tar, indices, PARAMETERS)

        # 保存 .npy
        save_dir_gen = os.path.join('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result', str(i), 'generated')
        save_dir_tar = os.path.join('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result', str(i), 'target')
        os.makedirs(save_dir_gen, exist_ok=True)
        os.makedirs(save_dir_tar, exist_ok=True)

        np.save(os.path.join(save_dir_gen, f'chr{z}.npy'), mat_gen)
        np.save(os.path.join(save_dir_tar, f'chr{z}.npy'), mat_tar)

        # 可视化
        visualize_generated_hic_contact_matrix(mat_gen, os.path.join(save_dir_gen, f'chr{z}_generated.pdf'))
        visualize_generated_hic_contact_matrix(mat_tar, os.path.join(save_dir_tar, f'chr{z}_target.pdf'))

        # 计算 SCC
        scc_score = SCC(mat_gen, mat_tar)
        scc_results.append({
            'cell_id': cell_id,
            'chr': str(z),
            'SCC': scc_score
        })

    # 清理内存
    del decoded_gen, decoded_tar, batch_gen, batch_tar
    torch.cuda.empty_cache()

# 保存 SCC 结果
scc_df = pd.DataFrame(scc_results)
scc_df.to_csv('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\scc_results.csv', index=False)
print("Average SCC:", scc_df['SCC'].mean())

print("Deconvolution done!!!")
