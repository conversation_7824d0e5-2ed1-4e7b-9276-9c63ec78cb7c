import torch
import lightning.pytorch as pl
from lightning.pytorch.loggers import TensorBoardLogger
from lightning.pytorch.callbacks import Model<PERSON><PERSON>ckpoint, EarlyStopping, LearningRateMonitor
from torch.utils.data import DataLoader
from Module3_model_optimized import MultiTaskModelOptimized, MultiTaskDataset
from Module3_utils import *
import warnings
warnings.filterwarnings('ignore')

def main():
    # 获取参数
    PARAMETERS = initialize_parameters_from_args()
    
    # 设置随机种子
    pl.seed_everything(PARAMETERS['seed'])
    
    # 创建数据集
    train_dataset = MultiTaskDataset(
        input_path=PARAMETERS['input_h5ad'], 
        target_csv_path=PARAMETERS['target_csv'], 
        target_h5ad_path=PARAMETERS['target_h5ad'], 
        patch_size=PARAMETERS['patch_size'], 
        mode='train'
    )
    
    val_dataset = MultiTaskDataset(
        input_path=PARAMETERS['input_h5ad'], 
        target_csv_path=PARAMETERS['target_csv'], 
        target_h5ad_path=PARAMETERS['target_h5ad'], 
        patch_size=PARAMETERS['patch_size'], 
        mode='val'
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=PARAMETERS['batch_size'], 
        shuffle=True, 
        num_workers=0
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=PARAMETERS['batch_size'], 
        shuffle=False, 
        num_workers=0
    )
    
    # 模型路径
    model_path = 'c:\\Users\\<USER>\\Desktop\\调整参数\\数据1\\数据1\\epoch=99-step=5400.ckpt'
    
    # 创建优化后的模型
    model = MultiTaskModelOptimized(
        patch_size=PARAMETERS['patch_size'],
        scA_B_dim=PARAMETERS['output_dim1'],
        model_path=model_path,
        input_dim=PARAMETERS['input_dim'],
        hidden_dim=PARAMETERS['hidden_dim'],
        output_emb_dim=PARAMETERS['output_emb_dim'],
        lr=PARAMETERS['learning_rate']
    )
    
    # 设置日志记录器 - 使用英文路径避免中文路径问题
    import os
    import tempfile
    # 使用临时目录避免中文路径问题
    temp_dir = tempfile.mkdtemp(prefix='tensorboard_')
    tb_logger = TensorBoardLogger(temp_dir, name='Module3_optimized_result')
    print(f"TensorBoard日志保存在: {temp_dir}")
    
    # 设置回调函数
    callbacks = [
        # 模型检查点 - 保存最佳模型
        ModelCheckpoint(
            monitor='val_loss3',  # 监控loss3
            mode='min',
            save_top_k=3,
            filename='best-{epoch:02d}-{val_loss3:.4f}',
            save_last=True
        ),
        
        # 早停 - 防止过拟合
        EarlyStopping(
            monitor='val_loss3',
            patience=15,  # 增加耐心值
            mode='min',
            verbose=True
        ),
        
        # 学习率监控
        LearningRateMonitor(logging_interval='epoch')
    ]
    
    # 创建训练器
    trainer = pl.Trainer(
        max_epochs=PARAMETERS['epochs'],
        logger=tb_logger,
        callbacks=callbacks,
        gradient_clip_val=PARAMETERS['gradient_clip_value'],
        accelerator='gpu' if torch.cuda.is_available() else 'cpu',
        devices=1,
        precision='16-mixed' if torch.cuda.is_available() else 32,  # 混合精度训练
        log_every_n_steps=10,
        val_check_interval=0.5,  # 每半个epoch验证一次
        accumulate_grad_batches=2,  # 梯度累积
        enable_progress_bar=True,
        enable_model_summary=True
    )
    
    print(f"开始训练优化后的模型...")
    print(f"训练样本数: {len(train_dataset)}")
    print(f"验证样本数: {len(val_dataset)}")
    print(f"批次大小: {PARAMETERS['batch_size']}")
    print(f"学习率: {PARAMETERS['learning_rate']}")
    print(f"设备: {'GPU' if torch.cuda.is_available() else 'CPU'}")
    
    # 开始训练
    trainer.fit(model, train_loader, val_loader)
    
    print("训练完成！")
    print(f"最佳模型保存在: {trainer.checkpoint_callback.best_model_path}")
    
    # 加载最佳模型进行最终评估
    if trainer.checkpoint_callback.best_model_path:
        best_model = MultiTaskModelOptimized.load_from_checkpoint(
            trainer.checkpoint_callback.best_model_path,
            patch_size=PARAMETERS['patch_size'],
            scA_B_dim=PARAMETERS['scA_B_dim'],
            model_path=model_path
        )
        
        # 在验证集上评估
        trainer.validate(best_model, val_loader)
        
        print("\n=== 训练总结 ===")
        print(f"最终验证损失: {trainer.logged_metrics.get('val_loss', 'N/A')}")
        print(f"最终loss3: {trainer.logged_metrics.get('val_loss3', 'N/A')}")
        print(f"最终loss3_scc: {trainer.logged_metrics.get('val_loss3_scc', 'N/A')}")

if __name__ == '__main__':
    main()