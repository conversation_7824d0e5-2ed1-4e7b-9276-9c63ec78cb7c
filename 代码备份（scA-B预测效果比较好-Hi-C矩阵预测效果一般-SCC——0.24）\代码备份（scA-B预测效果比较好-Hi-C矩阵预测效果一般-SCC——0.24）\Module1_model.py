import torch
import numpy as np
import pandas as pd
import torch.nn as nn
import lightning.pytorch as pl
import torchmetrics
#from Module1_utils import *
import torch.optim as optim
import glob
import os
#from Module1_global import *




class GenomicDataset(torch.utils.data.Dataset):
    """
    每个样本单独保存为一个 .pt 文件，
    这里只维护所有文件的路径列表，实际的数据加载放到 training_step 中
    """
    def __init__(self, data_dir: str, PARAMETERS: dict):
        super().__init__()
        # 搜集所有 .pt 文件路径
        self.file_paths = sorted(glob.glob(os.path.join(data_dir, '*.pt')))
        if len(self.file_paths) == 0:
            raise RuntimeError(f"No .pt files found in {data_dir}")
        self.PARAMETERS = PARAMETERS

    def __len__(self):
        return len(self.file_paths)

    def __getitem__(self, idx: int):
        # 仅返回文件路径，真正的加载延迟到 LightningModule 里
        return self.file_paths[idx]
    
class AutoEncoder(torch.nn.Module):
    def __init__(self, PARAMETERS):
        """
        Args:
            input_shape: 输入图像的大小 (Batch, Channel, Height, Width)，例如 (batch_size, 1, 128, 128) 或 (batch_size, 1, 512, 512)。
            embedding_dim: 编码后的一维嵌入的长度。
        """
        super(AutoEncoder, self).__init__()
        # 获取网络参数
        ndf = PARAMETERS['kernel_num']  # 卷积核数量
        embedding_dim = PARAMETERS['final_embedding']
        
        # 输入图像的尺寸
        input_height = PARAMETERS['num_nodes']
        input_width = PARAMETERS['num_nodes']

        # 计算卷积层输出的尺寸
        self.encoder = nn.Sequential(
            nn.Conv2d(in_channels=1, out_channels=ndf, kernel_size=4, stride=2, padding=1),  # 第一层卷积
            nn.BatchNorm2d(ndf),
            nn.ReLU(inplace=True),

            nn.Conv2d(in_channels=ndf, out_channels=2 * ndf, kernel_size=4, stride=2, padding=1),  # 第二层卷积
            nn.BatchNorm2d(2 * ndf),
            nn.LeakyReLU(0.2, inplace=True),

            nn.Conv2d(in_channels=2 * ndf, out_channels=4 * ndf, kernel_size=4, stride=2, padding=1),  # 第三层卷积
            nn.BatchNorm2d(4 * ndf),
            nn.LeakyReLU(0.2, inplace=True),

            nn.Conv2d(in_channels=4 * ndf, out_channels=8 * ndf, kernel_size=4, stride=2, padding=1),  # 第四层卷积
            nn.BatchNorm2d(8 * ndf),
            nn.LeakyReLU(0.2, inplace=True),
            
            nn.Flatten(),
            nn.Linear(8*ndf * (input_height // 16) * (input_width // 16), embedding_dim),  # 根据输入尺寸计算flatten后的维度
            nn.Sigmoid()
            
        )

        self.decoder = nn.Sequential(
            nn.Linear(embedding_dim, 8*ndf * (input_height // 16) * (input_width // 16)),  # 反向计算
            nn.Unflatten(1, (8*ndf, input_height // 16, input_width // 16)),

            nn.ConvTranspose2d(in_channels=8 * ndf, out_channels=4 * ndf, kernel_size=4, stride=2, padding=1),  # 第一层反卷积
            nn.BatchNorm2d(4 * ndf),
            nn.LeakyReLU(0.2, inplace=True),

            nn.ConvTranspose2d(in_channels=4 * ndf, out_channels=2 * ndf, kernel_size=4, stride=2, padding=1),  # 第二层反卷积
            nn.BatchNorm2d(2*ndf),
            nn.LeakyReLU(0.2, inplace=True),

            nn.ConvTranspose2d(in_channels=2 * ndf, out_channels=ndf, kernel_size=4, stride=2, padding=1),  # 第三层反卷积
            nn.BatchNorm2d(ndf),
            nn.LeakyReLU(0.2, inplace=True),

            nn.ConvTranspose2d(in_channels=ndf, out_channels=1, kernel_size=4, stride=2, padding=1),  # 第四层反卷积
            nn.BatchNorm2d(1),
            nn.ReLU(inplace=True),
        )
        
    def forward(self, x):
        encode = self.encoder(x)
        decode = self.decoder(encode)
        return encode, decode
    

class scGrapHiC(pl.LightningModule):
    def __init__(self, PARAMETERS, learning_rate=1e-3, weight_decay=1e-4):
        super().__init__()
        self.PARAMETERS = PARAMETERS
        self.autoencode = AutoEncoder(PARAMETERS)
        self.loss_func = torch.nn.MSELoss()
        #self.loss_func = torch.nn.BCEWithLogitsLoss()
        
        self.save_hyperparameters()

        # 优化器参数
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        
    
    def transform(self, bulk, dataset=False):
        #masks[masks == 0] = 0.5
        #output = output * masks
        bulk = bulk.view(bulk.shape[0], 1, bulk.shape[1], bulk.shape[2])
        embedding, output = self.autoencode(bulk)

        if dataset:
            return output, embedding
        else:
            return output
      

    def training_step(self, batch, batch_idx):
        """
        batch: list of .pt 文件路径 (由 Dataset 返回)
        逐条加载，并 map 到当前 GPU（self.device）
        """
        hics = []
        for path in batch:
            data = torch.load(path, map_location=self.device)
            # data['scHi_C_datas'] 是 [1, N, N] 或 [T, 1, N, N]，这里假设每文件一个样本
            hic = data['scHi_C_datas']
            hic = hic[:, 0, :, :]
            hics.append(hic)
        # 拼 batch
        hic_batch = torch.cat(hics, dim=0)  # [B,N,N]

        
        input = hic_batch.float()
        targets = hic_batch.float()
        #masks = batch['masks']
        output = self.transform(input)
                
        targets = targets.view(targets.shape[0], 1, targets.shape[1], targets.shape[2])
        loss = self.loss(output, targets)
        self.log("train/loss", loss, on_step=True, on_epoch=True, prog_bar=True, batch_size=input.shape[0], sync_dist=True)
        
        return loss

    def validation_step(self, batch, batch_idx):
        # 同 training_step 的加载逻辑
        hics = []
        for path in batch:
            data = torch.load(path, map_location=self.device)
            # data['scHi_C_datas'] 是 [1, N, N] 或 [T, 1, N, N]，这里假设每文件一个样本
            hic = data['scHi_C_datas']
            hic = hic[:, 0, :, :]
            hics.append(hic)
        # 拼 batch
        hic_batch = torch.cat(hics, dim=0)  # [B,N,N]


        input = hic_batch.float()
        targets = hic_batch.float()
        #masks = batch['masks']
        output = self.transform(input)
        
        targets = targets.view(targets.shape[0], 1, targets.shape[1], targets.shape[2])
        
        scores = run_evals(output, targets)
        loss = self.loss(output, targets)
        
        self.log("valid/loss", loss, on_step=True, on_epoch=True, prog_bar=True, batch_size=input.shape[0], sync_dist=True)
        #print('valid/loss', loss)
        
        self.log("valid/MSE", np.mean(scores['MSE']), batch_size=input.shape[0], sync_dist=True)
        self.log("valid/SSIM", np.mean(scores['SSIM']), batch_size=input.shape[0], sync_dist=True)
        self.log("valid/GD", np.mean(scores['GD']), batch_size=input.shape[0], sync_dist=True)
        self.log("valid/SCC", np.mean(scores['SCC']), batch_size=input.shape[0], sync_dist=True)

        results_file = os.path.join(self.PARAMETERS['result_dir'], self.PARAMETERS['experiment'], 'results{}_{}.csv'.format(self.PARAMETERS['kernel_num'],self.PARAMETERS['final_embedding']))
        
        for i in range(output.shape[0]):
            with open(results_file, 'a+') as f:
                f.write(
                    '{},{},{},{}\n'.format(
                    scores['MSE'][i],
                    scores['SSIM'][i],
                    scores['GD'][i],
                    scores['SCC'][i]
                )
            )

        return loss

    def test_step(self, batch, batch_idx):
        # 同理加载
        hics, indexes, metadatas = [], [], []
        for path in batch:
            data = torch.load(path, map_location=self.device)
            # data['scHi_C_datas'] 是 [1, N, N] 或 [T, 1, N, N]，这里假设每文件一个样本
            hic = data['scHi_C_datas']
            hic = hic[:, 0, :, :]
            hics.append(hic)
            indexes.append(data['indexes'])
            
            # 提取文件名，去掉路径和文件的后缀
            filename = os.path.basename(path)
            filename_without_suffix = os.path.splitext(filename)[0]
            metadatas.extend([filename_without_suffix] * hic.shape[0])


        hic_batch = torch.cat(hics, dim=0)
        index_batch = torch.cat(indexes, dim=0)
        #metadata_batch = torch.cat(metadatas, dim=0)
        # 确保metadatas与hic_batch的样本数一致
        assert len(metadatas) == hic_batch.shape[0], "Metadatas length does not match hic_batch size"

        input = hic_batch.float()
        targets = hic_batch.float()
        indexes = index_batch
        #metadatas = metadata_batch
        #masks = batch['masks']
        
        output, graph_latents = self.transform(input, dataset=True)
        
        targets = targets.view(targets.shape[0], 1, targets.shape[1], targets.shape[2])
        
        scores = run_evals(output, targets)
        
        for i in range(output.shape[0]):
            log_results(
                output[i, 0, :, :],
                targets[i, 0, :, :],
                [scores['MSE'][i], scores['SSIM'][i], scores['GD'][i], scores['SCC'][i]],
                indexes[i, :],
                metadatas[i],
                graph_latents[i,:],
                self.PARAMETERS,
                save=False
            )


    
    def loss(self, generated, targets):
        loss = self.loss_func(
            generated*self.PARAMETERS['loss_scale'], 
            targets*self.PARAMETERS['loss_scale']
        )
        return loss
        

    #def configure_optimizers(self):
        #default_lr = 1e-3  # 设置默认学习率，例如 0.001
        #optimizer = optim.AdamW(self.parameters())
        #return optimizer
    def configure_optimizers(self):
        # 使用 AdamW 并带有 weight_decay
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay
        )
        # 使用 ReduceLROnPlateau 调度器，监听验证集损失
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',            # 监控指标是否越小越好
            factor=0.1,            # 学习率调整倍数（0.1 表示降为原来的 1/10）
            patience=1,            # 容忍指标不下降的 epoch 数
            min_lr=1e-6,           # 学习率下限
            verbose=True           # 是否打印调度信息
        )
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'interval': 'epoch',
                'monitor': 'train/loss_epoch',  # 监听训练集 Loss
            }
        } 
    def transform_embedding_to_matrix(self, embedding_matrix):
        """
        对输入的embedding矩阵的每一行进行处理。
        每一行根据PARAMETERS['final_embedding']分割，每个子向量通过decoder解码为矩阵，
        然后将矩阵展平成一维向量，最终重新组合成新的矩阵。

        Args:
            embedding_matrix (torch.Tensor): 输入的embedding矩阵，形状为 (num_rows, embedding_dim)。

        Returns:
            torch.Tensor: 处理后的矩阵，形状为 (num_rows, new_embedding_dim)。
        """
        final_embedding = self.PARAMETERS['final_embedding']  # 每个子向量的长度
        batch_size, embedding_dim = embedding_matrix.shape

        # 确保embedding_dim是final_embedding的整数倍
        assert embedding_dim % final_embedding == 0, \
            "embedding_dim必须是final_embedding的整数倍"

        num_segments = embedding_dim // final_embedding  # 每行分割的子向量数量
        new_rows = []

        for i in range(batch_size):
            row = embedding_matrix[i]  # 提取当前行
            row = torch.tensor(row)
            sub_vectors = row.view(num_segments, final_embedding)  # 分割为若干子向量
            decoded_vectors = []

            for sub_vector in sub_vectors:
                # 对每个子向量通过decoder解码为矩阵
                sub_vector = sub_vector.view(1, -1).float()  # 添加批次维度以符合解码器输入
                with torch.no_grad():
                    decoded_matrix = self.autoencode.decoder(sub_vector)
                flattened_vector = decoded_matrix.view(-1)  # 将解码后的矩阵展平为一维向量
                decoded_vectors.append(flattened_vector)

            # 将解码后的向量重新组合为一行
            new_row = torch.cat(decoded_vectors, dim=0)
            new_rows.append(new_row)

        # 将处理后的所有行组合为新的矩阵
        new_embedding_matrix = torch.stack(new_rows, dim=0)

        return new_embedding_matrix
