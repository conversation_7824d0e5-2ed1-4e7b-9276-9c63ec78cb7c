#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据调试脚本 - 检查数据文件和ID匹配
"""

import anndata as ad
import pandas as pd
import numpy as np
from Module3_utils import initialize_parameters_from_args

def debug_data():
    try:
        print("=== 开始调试数据文件 ===")
        
        # 获取参数
        PARAMETERS = initialize_parameters_from_args()
        
        # 1. 检查输入文件
        print("\n1. 检查输入embedding文件...")
        input_path = PARAMETERS['input_h5ad']
        print(f"   文件路径: {input_path}")
        
        input_ad = ad.read_h5ad(input_path)
        print(f"   形状: {input_ad.shape}")
        print(f"   观测索引前5个: {list(input_ad.obs.index[:5])}")
        print(f"   观测索引类型: {type(input_ad.obs.index[0])}")
        
        # 2. 检查目标CSV文件
        print("\n2. 检查scA/B CSV文件...")
        target_csv_path = PARAMETERS['target_csv']
        print(f"   文件路径: {target_csv_path}")
        
        target_df = pd.read_csv(target_csv_path)
        print(f"   形状: {target_df.shape}")
        print(f"   列名前5个: {list(target_df.columns[:5])}")
        
        # 去掉前两列后的列名
        scab_columns = target_df.columns[2:]
        print(f"   scA/B列数: {len(scab_columns)}")
        print(f"   scA/B列名前5个: {list(scab_columns[:5])}")
        print(f"   scA/B列名类型: {type(scab_columns[0]) if len(scab_columns) > 0 else 'None'}")
        
        # 3. 检查目标h5ad文件
        print("\n3. 检查目标embedding文件...")
        target_h5ad_path = PARAMETERS['target_h5ad']
        print(f"   文件路径: {target_h5ad_path}")
        
        target_ad = ad.read_h5ad(target_h5ad_path)
        print(f"   形状: {target_ad.shape}")
        print(f"   观测索引前5个: {list(target_ad.obs.index[:5])}")
        print(f"   观测索引类型: {type(target_ad.obs.index[0])}")
        
        # 4. 检查ID匹配
        print("\n4. 检查ID匹配情况...")
        input_ids = set(input_ad.obs.index)
        target_ids = set(target_ad.obs.index)
        scab_ids = set(scab_columns)
        
        print(f"   输入embedding ID数量: {len(input_ids)}")
        print(f"   目标embedding ID数量: {len(target_ids)}")
        print(f"   scA/B ID数量: {len(scab_ids)}")
        
        # 计算交集
        common_input_target = input_ids & target_ids
        common_input_scab = input_ids & scab_ids
        common_target_scab = target_ids & scab_ids
        common_all = input_ids & target_ids & scab_ids
        
        print(f"   输入&目标embedding共同ID: {len(common_input_target)}")
        print(f"   输入embedding&scA/B共同ID: {len(common_input_scab)}")
        print(f"   目标embedding&scA/B共同ID: {len(common_target_scab)}")
        print(f"   三者共同ID: {len(common_all)}")
        
        if len(common_all) > 0:
            print(f"   共同ID前5个: {sorted(list(common_all))[:5]}")
        else:
            print("   ❌ 没有找到共同的ID！")
            print("   ")  
            print("   输入embedding ID示例:")
            print(f"     {sorted(list(input_ids))[:10]}")
            print("   目标embedding ID示例:")
            print(f"     {sorted(list(target_ids))[:10]}")
            print("   scA/B ID示例:")
            print(f"     {sorted(list(scab_ids))[:10]}")
            
            # 检查ID格式差异
            print("\n   检查ID格式差异...")
            if len(input_ids) > 0 and len(scab_ids) > 0:
                input_sample = list(input_ids)[0]
                scab_sample = list(scab_ids)[0]
                print(f"   输入ID示例: '{input_sample}' (类型: {type(input_sample)})")
                print(f"   scA/B ID示例: '{scab_sample}' (类型: {type(scab_sample)})")
                
        print("\n=== 数据调试完成 ===")
        
    except Exception as e:
        print(f"\n=== 发现错误 ===")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_data()