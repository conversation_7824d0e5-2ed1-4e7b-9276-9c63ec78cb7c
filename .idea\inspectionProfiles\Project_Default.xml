<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="10">
            <item index="0" class="java.lang.String" itemvalue="tqdm" />
            <item index="1" class="java.lang.String" itemvalue="scipy" />
            <item index="2" class="java.lang.String" itemvalue="transformers" />
            <item index="3" class="java.lang.String" itemvalue="seaborn" />
            <item index="4" class="java.lang.String" itemvalue="matplotlib" />
            <item index="5" class="java.lang.String" itemvalue="dgl" />
            <item index="6" class="java.lang.String" itemvalue="torch" />
            <item index="7" class="java.lang.String" itemvalue="numpy" />
            <item index="8" class="java.lang.String" itemvalue="bertviz" />
            <item index="9" class="java.lang.String" itemvalue="argparse" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E501" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>