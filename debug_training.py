#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import torch
import lightning.pytorch as pl
from Module3_model_optimized import MultiTaskModelOptimized, MultiTaskDataset
from Module3_utils import initialize_parameters_from_args
import traceback

def debug_training():
    try:
        print("=== 开始调试训练过程 ===")
        
        # 1. 测试参数初始化
        print("1. 初始化参数...")
        PARAMETERS = initialize_parameters_from_args()
        print(f"   参数初始化成功: batch_size={PARAMETERS['batch_size']}, patch_size={PARAMETERS['patch_size']}")
        
        # 2. 设置种子
        print("2. 设置随机种子...")
        pl.seed_everything(42)
        print("   种子设置完成")
        
        # 3. 测试数据集创建
        print("3. 创建数据集...")
        print(f"   输入文件: {PARAMETERS['input_h5ad']}")
        print(f"   目标CSV: {PARAMETERS['target_csv']}")
        print(f"   目标H5AD: {PARAMETERS['target_h5ad']}")
        
        train_dataset = MultiTaskDataset(
            PARAMETERS['input_h5ad'], 
            PARAMETERS['target_csv'], 
            PARAMETERS['target_h5ad'], 
            PARAMETERS['patch_size'], 
            mode='train'
        )
        print(f"   训练数据集创建成功，大小: {len(train_dataset)}")
        
        # 4. 测试数据加载
        print("4. 测试数据加载...")
        sample = train_dataset[0]
        print(f"   样本形状: input={sample[0].shape}, scab={sample[1].shape}, target={sample[2].shape}")
        
        # 5. 测试模型创建
        print("5. 创建模型...")
        model = MultiTaskModelOptimized(
            patch_size=PARAMETERS['patch_size'],
            scA_B_dim=PARAMETERS['output_dim1'],
            model_path='c:\\Users\\<USER>\\Desktop\\调整参数\\数据1\\数据1\\epoch=99-step=5400.ckpt',
            input_dim=PARAMETERS['input_dim'],
            hidden_dim=PARAMETERS['hidden_dim'],
            output_emb_dim=768,
            lr=PARAMETERS['learning_rate']
        )
        print("   模型创建成功")
        
        # 6. 测试前向传播
        print("6. 测试前向传播...")
        model.eval()
        with torch.no_grad():
            input_batch = sample[0].unsqueeze(0)  # 添加batch维度
            output1, output2 = model(input_batch)
            print(f"   前向传播成功: output1={output1.shape}, output2={output2.shape}")
        
        print("\n=== 所有测试通过！训练应该可以正常进行 ===")
        
    except Exception as e:
        print(f"\n=== 发现错误 ===")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        print("\n详细错误堆栈:")
        traceback.print_exc()
        
if __name__ == "__main__":
    debug_training()