import numpy as np
import torch
import os
from scipy.stats import pearsonr
from skimage.metrics import structural_similarity as ssim

def run_evals(output, targets):
    """
    计算模型输出与目标之间的评估指标
    
    Args:
        output: 模型输出 (batch_size, 1, height, width)
        targets: 目标值 (batch_size, 1, height, width)
    
    Returns:
        dict: 包含MSE, SSIM, GD, SCC指标的字典
    """
    batch_size = output.shape[0]
    
    mse_scores = []
    ssim_scores = []
    gd_scores = []
    scc_scores = []
    
    # 转换为numpy数组进行计算
    output_np = output.detach().cpu().numpy()
    targets_np = targets.detach().cpu().numpy()
    
    for i in range(batch_size):
        # 提取单个样本
        pred = output_np[i, 0]  # (height, width)
        true = targets_np[i, 0]  # (height, width)
        
        # MSE
        mse = np.mean((pred - true) ** 2)
        mse_scores.append(mse)
        
        # SSIM
        ssim_score = ssim(true, pred, data_range=pred.max() - pred.min())
        ssim_scores.append(ssim_score)
        
        # Genomic Distance (GD) - 简化版本
        gd = np.mean(np.abs(pred - true))
        gd_scores.append(gd)
        
        # SCC (Spearman Correlation Coefficient)
        pred_flat = pred.flatten()
        true_flat = true.flatten()
        if len(np.unique(pred_flat)) > 1 and len(np.unique(true_flat)) > 1:
            scc, _ = pearsonr(pred_flat, true_flat)
            if np.isnan(scc):
                scc = 0.0
        else:
            scc = 0.0
        scc_scores.append(scc)
    
    return {
        'MSE': mse_scores,
        'SSIM': ssim_scores,
        'GD': gd_scores,
        'SCC': scc_scores
    }

def log_results(output, target, scores, indexes, metadata, graph_latent, PARAMETERS, save=True):
    """
    记录和保存结果
    
    Args:
        output: 模型输出矩阵
        target: 目标矩阵
        scores: 评估分数列表 [MSE, SSIM, GD, SCC]
        indexes: 索引信息
        metadata: 元数据
        graph_latent: 图嵌入
        PARAMETERS: 参数字典
        save: 是否保存结果
    """
    if save:
        # 创建结果目录
        result_dir = PARAMETERS.get('result_dir', './results')
        experiment = PARAMETERS.get('experiment', 'default')
        save_dir = os.path.join(result_dir, experiment)
        os.makedirs(save_dir, exist_ok=True)
        
        # 保存结果文件
        results_file = os.path.join(save_dir, f'detailed_results_{metadata}.csv')
        
        with open(results_file, 'w') as f:
            f.write('MSE,SSIM,GD,SCC\n')
            f.write(f'{scores[0]},{scores[1]},{scores[2]},{scores[3]}\n')
        
        # 保存嵌入
        embedding_file = os.path.join(save_dir, f'embedding_{metadata}.pt')
        torch.save(graph_latent, embedding_file)
        
        print(f"Results saved for {metadata}: MSE={scores[0]:.4f}, SSIM={scores[1]:.4f}, GD={scores[2]:.4f}, SCC={scores[3]:.4f}")
    else:
        print(f"Results for {metadata}: MSE={scores[0]:.4f}, SSIM={scores[1]:.4f}, GD={scores[2]:.4f}, SCC={scores[3]:.4f}")