import argparse
import anndata
from anndata import AnnData
import numpy as np
import pandas as pd
import scanpy as sc
from typing import Union, Tuple
import torch
import math
import copy
import warnings
from tqdm import tqdm
import torch

import numpy as np
import pandas as pd
import scipy.sparse as sps
import scipy.stats as stats

from sklearn import metrics
from scipy.sparse import csr_matrix
from scipy.stats import pearsonr, spearmanr
from skimage.metrics import structural_similarity

try:
	from scipy.stats import PearsonRConstantInputWarning
except:
	from scipy.stats import ConstantInputWarning as PearsonRConstantInputWarning
from torch.distributions import Normal, Dirichlet
from scipy.special import expit
import os
import matplotlib.pyplot as plt
plt.rcParams['svg.fonttype'] = 'none'
plt.rcParams['svg.hashsalt'] = 'hello'
from matplotlib.ticker import StrMethodFormatter
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.patches import FancyArrowPatch

import random
REDMAP = LinearSegmentedColormap.from_list("bright_red", [(1,1,1),(1,0,0)])
def initialize_parameters_from_args():
    parser = argparse.ArgumentParser(description="Multi-task learning model for scA/B and embedding reconstruction")
    parser.add_argument('--input_h5ad', type=str, default='c:\\Users\\<USER>\\Desktop\\调整参数\\数据1\\数据1\\scRNA_embedding_train.h5ad', help='Path to input embedding h5ad file')
    parser.add_argument('--target_csv', type=str, default='c:\\Users\\<USER>\\Desktop\\调整参数\\数据1\\数据1\\with_scRNA_30000_cutoff_scA_B.csv', help='Path to scA/B score csv file')
    parser.add_argument('--target_h5ad', type=str, default='c:\\Users\\<USER>\\Desktop\\调整参数\\test_64x64_768dim_embeddings\\test_64x64_768dim_embeddings.h5ad', help='Path to target embedding h5ad file')
    parser.add_argument('--patch_size', type=int, default=95, help='Patch size for target embedding')
    parser.add_argument('--batch_size', type=int, default=64, help='Batch size')
    parser.add_argument('--lr', type=float, default=1e-3, help='Learning rate')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--seed', type=int, default=42, help='Random Seed')
    parser.add_argument('--scA_B_dim', type=int, default=2574, help='scA/B score dim')
    parser.add_argument('--gradient_clip_value', type=float, default=0.1, help='Gradient clipping')#梯度裁剪cutoff，以避免梯度爆炸

    #将大矩阵切分成小矩阵时用到的参数
    parser.add_argument('--stride', type=int, default=64, help='Stride')
    parser.add_argument('--num_nodes', type=int, default=64, help='Width/Height of nodes')#分成的每个小块
    parser.add_argument('--final_embedding', type=int, default=768, help='Encoder output dim')

    args, unknown = parser.parse_known_args()
    
    parameters = vars(args)  # Convert Namespace to dictionary
    
    return parameters


def create_directory(path):
    if not os.path.exists(path):
        try:
            # Create the directory
            os.makedirs(path)
        except OSError as e:
            print(f"Error: {e}")





def load_and_merge_data(file_path):
    """
    加载 .npz 文件中的数据，并将 'indexes' 和 'metadatas' 数组合并为一个 DataFrame。

    参数:
    file_path (str): 包含 'indexes' 和 'metadatas' 数组的 .npz 文件路径。

    返回:
    pd.DataFrame: 合并后的 DataFrame，包含 'cell_id', 'chr', 'original_size', 'row_id', 'col_id' 列。
    """
    # 加载数据
    all_data_array = torch.load(file_path)

    # 提取数组
    indexes_array = all_data_array['indexes']
    metadatas_array = all_data_array['metadatas']

    # 转换数据类型
    #metadatas_array = metadatas_array.astype(int)

    # 创建 DataFrame
    df_metadatas = pd.DataFrame(metadatas_array, columns=['chr', 'original_size'])
    df_indexes = pd.DataFrame(indexes_array, columns=['chr', 'original_size', 'row_id', 'col_id'])

    # 合并 DataFrame
    df_metadatas['row_id'] = df_indexes['row_id']
    df_metadatas['col_id'] = df_indexes['col_id']

    return df_metadatas

def reconstruct_matrix(chunked_matrices, indices, params):
    """
    @params: chunked_matrices <list<np.array>> 分块后的矩阵
    @params: indices <list<tuple>> 每个子矩阵的原始位置
    @params: original_size <int> 原始矩阵的大小
    @returns: <np.array> 还原的原始矩阵
    """
    # 创建一个全零的原始矩阵
    #print(chunked_matrices.shape)
    S = chunked_matrices.shape[0]          # 已知总和
    n = int((math.isqrt(8 * S + 1) - 1) // 2)
    new_size = n * params['stride']
    
    new_size = int(new_size)
    reconstructed_matrix = np.zeros((new_size, new_size))
    #print(reconstructed_matrix.shape)
    #chunked_matrices = np.squeeze(chunked_matrices, axis=1)
    original_size = 0

    # 遍历每个子矩阵和对应的索引
    for chunk, (chr_num, size, i, j) in zip(chunked_matrices, indices):
        #print(chunk.shape, i, j)
        reconstructed_matrix[i:i+chunk.shape[0], j:j+chunk.shape[1]] = chunk                                                                                                                                                          
        original_size = size
        #print(original_size)
    reconstructed_matrix = reconstructed_matrix[0:original_size, 0:original_size]
    
    
    # 遍历矩阵的下三角（不包括对角线）
    for i in range(1, original_size):          # 从第1行开始即可
        for j in range(i):                   # j < i 保证只扫下三角
            reconstructed_matrix[j, i] = reconstructed_matrix[i, j]
    return reconstructed_matrix


def visualize_generated_hic_contact_matrix(generated, output_file, cmap=REDMAP, dpi=300):
    """
    将生成的 Hi-C 联系矩阵绘制并保存到文件。

    参数
    ----
    generated : array-like
        需要可视化的 Hi-C 联系矩阵。
    output_file : str
        图像保存路径（含文件名和扩展名）。
    cmap : str or Colormap, optional
        Matplotlib colormap，默认 'Reds'。
    dpi : int, optional
        图像分辨率，默认 300。
    """
    # 创建 Figure 和 Axes
    fig, ax = plt.subplots()

    # 绘制矩阵
    img = ax.matshow(generated, cmap=cmap)

    # 不显示坐标轴
    ax.axis('off')

    # 保存图像
    fig.savefig(output_file, bbox_inches='tight', dpi=dpi)
    #plt.show(fig)

    # 关闭 figure，防止内存泄漏
    plt.close(fig)

def visualize_reconstruct_matrix_2(id, df_metadatas, decoded_results, PARAMETERS):
    """
    处理每个细胞和染色体的数据，生成和目标矩阵，并可视化结果。

    参数:
    df_metadatas (pd.DataFrame): 包含细胞和染色体信息的 DataFrame。
    PARAMETERS (dict): 包含参数的字典。
    """
    RESULTS = 'c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\'
    df_cell_all = df_metadatas
    i = 0
    for z in df_cell_all['chr'].unique():
        df_cell = df_cell_all[df_cell_all['chr'] == z]
        array_chr_generated = np.zeros((len(df_cell), PARAMETERS['stride'], PARAMETERS['num_nodes']))
        reconstruct_output_folder = os.path.join(
            RESULTS, str(id), 'generated'
        )
        os.makedirs(reconstruct_output_folder, exist_ok=True)  # 确保输出文件夹存在

            
        df_cell = df_cell.reset_index(drop=True)
        for j in range(0, len(df_cell)):
            
            file_name = 'chr{}_s{}_e{}'.format(df_cell.loc[j, 'chr'], df_cell.loc[j, 'row_id'], df_cell.loc[j, 'col_id'])
            #file_path_generated = os.path.join(RESULTS, PARAMETERS['experiment'], PARAMETERS['dataset_name'], str(i), 'generated', '{}.npy'.format(file_name))
            array_chr_chunk_generated = decoded_results[i].cpu()
            #np.save(file_path_generated, array_chr_chunk_generated)
            array_chr_generated[j, :, :] = array_chr_chunk_generated[0, :, :]

            i = i + 1


        indices_array = df_cell[['chr', 'original_size', 'row_id', 'col_id']].to_numpy()
        reconstructed_matrix_generated = reconstruct_matrix(array_chr_generated, indices_array, PARAMETERS)
        file_name = 'chr{}'.format(str(z))
        file_path_generated = os.path.join(reconstruct_output_folder, '{}.npy'.format(file_name))
        np.save(file_path_generated, reconstructed_matrix_generated)

        
        reconstruct_output_file = os.path.join(reconstruct_output_folder, 'chr{}_generated.pdf'.format(str(z)))

        visualize_generated_hic_contact_matrix(reconstructed_matrix_generated, reconstruct_output_file)



def vstrans(d1, d2):
    """
    Variance stabilizing transformation to normalize read counts before computing
    stratum correlation. This normalizes counts so that different strata share similar
    dynamic ranges.
    Parameters
    ----------
    d1 : numpy.array of floats
        Diagonal of the first matrix.
    d2 : numpy.array of floats
        Diagonal of the second matrix.
    Returns
    -------
    r2k : numpy.array of floats
        Array of weights to use to normalize counts.
    """
    # Get ranks of counts in diagonal
    ranks_1 = np.argsort(d1) + 1
    ranks_2 = np.argsort(d2) + 1
    # Scale ranks betweeen 0 and 1
    nranks_1 = ranks_1 / max(ranks_1)
    nranks_2 = ranks_2 / max(ranks_2)
    nk = len(ranks_1)
    r2k = np.sqrt(np.var(nranks_1 / nk) * np.var(nranks_2 / nk))
    return r2k

def SCC(A:np.ndarray, B:np.ndarray, max_bins:int=20, correlation_method:str='SCC'):
    """
        Compute the stratum-adjusted correlation coefficient (SCC) between two
        Hi-C matrices up to max_dist. A Pearson correlation coefficient is computed
        for each diagonal in the range of 0 to max_dist and a weighted sum of those
        coefficients is returned.
        Parameters
        ----------
        mat1 : scipy.sparse.csr_matrix
            First matrix to compare.
        mat2 : scipy.sparse.csr_matrix
            Second matrix to compare.
        max_bins : int
            Maximum distance at which to consider, in bins.
        Returns
        -------
        scc : float
            Stratum adjusted correlation coefficient.
    """
    if (len(A.shape) != 2) and (len(B.shape) != 2):
        raise ValueError("both input matrices are of the wrong shape")
    elif len(A.shape) != 2:
        raise ValueError("first input matrix is of the wrong shape (" + str(len(A.shape)) + " dimensions instead of 2)")
    elif len(B.shape) != 2:
        raise ValueError("second input matrix is of the wrong shape (" + str(len(B.shape)) + " dimensions instead of 2)")
    elif A.shape != B.shape:
        raise KeyError("matrices not of the same size")
	
    if max_bins < 0 or max_bins > int(A.shape[0] - 5):
        max_bins = int(A.shape[0] - 5)


    mat1 = csr_matrix(A)
    mat2 = csr_matrix(B)
    
    corr_diag = np.zeros(len(range(max_bins)))
    weight_diag = corr_diag.copy()
    
    for d in range(max_bins):
        d1 = mat1.diagonal(d)
        d2 = mat2.diagonal(d)
        mask = (~np.isnan(d1)) & (~np.isnan(d2))
        d1 = d1[mask]
        d2 = d2[mask]
        # Silence NaN warnings: this happens for empty diagonals and will
        # not be used in the end.
        with warnings.catch_warnings():
            warnings.filterwarnings(
                "ignore", category=PearsonRConstantInputWarning
            )
            if correlation_method == 'PCC':
                cor = pearsonr(d1, d2)[0]

            elif correlation_method == 'SCC':
                cor = spearmanr(d1, d2)[0]
            else:
                print('Provided invalid correlation type')
                exit(1)
            corr_diag[d] = cor

        r2k = vstrans(d1, d2)
        weight_diag[d] = len(d1) * r2k

    corr_diag, weight_diag = corr_diag[1:], weight_diag[1:]
    mask = ~np.isnan(corr_diag)
    
    corr_diag, weight_diag = corr_diag[mask], weight_diag[mask]
    
    # Normalize weights
    weight_diag /= sum(weight_diag)
    
    # Weighted sum of coefficients to get SCCs
    scc = np.nansum(corr_diag * weight_diag)
    
    return scc



# 在 Module3_utils.py 添加
def pearson_loss(pred, target):
    pred = pred - pred.mean(dim=1, keepdim=True)
    target = target - target.mean(dim=1, keepdim=True)
    corr_per_sample = (pred * target).sum(dim=1) / (pred.std(dim=1) * target.std(dim=1) + 1e-8)
    return (1 - corr_per_sample).mean()

def scc_loss(decoded_out, decoded_y, patch_size, num_nodes=64, num_samples=20):
    """
    计算每个样本的每个patch的SCC，平均作为损失。
    decoded_out/decoded_y: (batch, patch_size * num_nodes**2)
    """
    scc_scores = []
    flat_size = num_nodes ** 2  # e.g., 64*64=4096
    
    # 确保形状正确
    if decoded_out.shape[1] != patch_size * flat_size:
        raise ValueError(f"展平大小不匹配: 期望 {patch_size * flat_size}, 实际 {decoded_out.shape[1]}")
    
    decoded_out = decoded_out.cpu().numpy()  # 批量转numpy
    decoded_y = decoded_y.cpu().numpy()
    
    for i in range(decoded_out.shape[0]):
        out_flat = decoded_out[i]
        y_flat = decoded_y[i]
        
        # 重塑回 (patch_size, num_nodes, num_nodes)
        out_patches = out_flat.reshape(patch_size, num_nodes, num_nodes)
        y_patches = y_flat.reshape(patch_size, num_nodes, num_nodes)
        
        sample_indices = random.sample(range(patch_size), min(num_samples, patch_size))
        patch_sccs = []
        for p in sample_indices:
            scc = SCC(out_patches[p], y_patches[p])
            patch_sccs.append(scc)
        
        scc_scores.append(np.mean(patch_sccs))
    
    mean_scc = np.mean(scc_scores)
    return 1 - torch.tensor(mean_scc, dtype=torch.float32)  # 作为损失，返回tensor