import torch
import lightning.pytorch as pl
from lightning.pytorch.loggers import <PERSON>sorBoardLogger
from torch.utils.data import DataLoader
from Module3_model_optimized import MultiTaskModelOptimized, MultiTaskDataset
from Module3_utils import *
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

def evaluate_model_performance(model, test_loader, device):
    """评估模型性能并生成详细报告"""
    model.eval()
    all_loss1, all_loss2, all_loss3 = [], [], []
    all_loss3_mse, all_loss3_scc = [], []
    
    with torch.no_grad():
        for batch in test_loader:
            x, y1, y2, _ = batch
            x, y1, y2 = x.to(device), y1.to(device), y2.to(device)
            
            out1, out2 = model(x)
            decoded_y2 = model._decode_embedding_to_hic_flat(y2)
            decoded_out2 = model._decode_embedding_to_hic_flat(out2)
            
            # 计算各项损失
            loss1 = torch.nn.functional.mse_loss(out1, y1)
            loss2 = torch.nn.functional.mse_loss(out2, y2)
            loss3_mse = torch.nn.functional.mse_loss(decoded_out2, decoded_y2)
            loss3_scc = scc_loss(decoded_out2, decoded_y2, model.patch_size)
            loss3 = 0.4 * loss3_mse + 0.6 * loss3_scc
            
            all_loss1.append(loss1.item())
            all_loss2.append(loss2.item())
            all_loss3.append(loss3.item())
            all_loss3_mse.append(loss3_mse.item())
            all_loss3_scc.append(loss3_scc.item())
    
    return {
        'loss1_mean': np.mean(all_loss1),
        'loss1_std': np.std(all_loss1),
        'loss2_mean': np.mean(all_loss2),
        'loss2_std': np.std(all_loss2),
        'loss3_mean': np.mean(all_loss3),
        'loss3_std': np.std(all_loss3),
        'loss3_mse_mean': np.mean(all_loss3_mse),
        'loss3_mse_std': np.std(all_loss3_mse),
        'loss3_scc_mean': np.mean(all_loss3_scc),
        'loss3_scc_std': np.std(all_loss3_scc)
    }

def plot_loss_comparison(original_results, optimized_results, save_path):
    """绘制损失对比图"""
    metrics = ['loss1_mean', 'loss2_mean', 'loss3_mean', 'loss3_mse_mean', 'loss3_scc_mean']
    labels = ['Loss1 (Head1)', 'Loss2 (Head2)', 'Loss3 (Total)', 'Loss3 MSE', 'Loss3 SCC']
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    for i, (metric, label) in enumerate(zip(metrics, labels)):
        if i < len(axes):
            ax = axes[i]
            
            # 原始模型和优化模型的对比
            original_val = original_results.get(metric, 0)
            optimized_val = optimized_results.get(metric, 0)
            
            bars = ax.bar(['Original', 'Optimized'], [original_val, optimized_val], 
                         color=['lightcoral', 'lightblue'])
            ax.set_title(label)
            ax.set_ylabel('Loss Value')
            
            # 添加数值标签
            for bar, val in zip(bars, [original_val, optimized_val]):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{val:.4f}', ha='center', va='bottom')
    
    # 隐藏多余的子图
    for i in range(len(metrics), len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"损失对比图已保存到: {save_path}")

def main():
    # 获取参数
    PARAMETERS = initialize_parameters_from_args()
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建测试数据集
    test_dataset = MultiTaskDataset(
        PARAMETERS['input_h5ad'], 
        PARAMETERS['target_csv'], 
        PARAMETERS['target_h5ad'], 
        PARAMETERS['patch_size'], 
        mode='test'
    )
    
    test_loader = DataLoader(
        test_dataset, 
        batch_size=PARAMETERS['batch_size'], 
        shuffle=False, 
        num_workers=4
    )
    
    print(f"测试样本数: {len(test_dataset)}")
    
    # 模型路径
    model_path = 'c:\\Users\\<USER>\\Desktop\\调整参数\\数据1\\数据1\\epoch=99-step=5400.ckpt'
    
    # 查找最佳优化模型检查点
    result_dir = 'c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_optimized_result'
    
    try:
        # 尝试找到最佳模型
        import os
        import glob
        
        checkpoint_pattern = os.path.join(result_dir, '**', 'best-*.ckpt')
        checkpoint_files = glob.glob(checkpoint_pattern, recursive=True)
        
        if checkpoint_files:
            # 选择最新的检查点
            best_checkpoint = max(checkpoint_files, key=os.path.getctime)
            print(f"找到最佳模型检查点: {best_checkpoint}")
            
            # 加载优化后的模型
            optimized_model = MultiTaskModelOptimized.load_from_checkpoint(
                best_checkpoint,
                patch_size=PARAMETERS['patch_size'],
                scA_B_dim=PARAMETERS['scA_B_dim'],
                model_path=model_path
            ).to(device)
            
        else:
            print("未找到训练好的优化模型，使用默认初始化模型")
            optimized_model = MultiTaskModelOptimized(
                patch_size=PARAMETERS['patch_size'],
                scA_B_dim=PARAMETERS['scA_B_dim'],
                model_path=model_path
            ).to(device)
            
    except Exception as e:
        print(f"加载优化模型时出错: {e}")
        print("使用默认初始化的优化模型")
        optimized_model = MultiTaskModelOptimized(
            patch_size=PARAMETERS['patch_size'],
            scA_B_dim=PARAMETERS['scA_B_dim'],
            model_path=model_path
        ).to(device)
    
    # 评估优化后的模型
    print("\n=== 评估优化后的模型 ===")
    optimized_results = evaluate_model_performance(optimized_model, test_loader, device)
    
    print(f"Loss1 (Head1): {optimized_results['loss1_mean']:.6f} ± {optimized_results['loss1_std']:.6f}")
    print(f"Loss2 (Head2): {optimized_results['loss2_mean']:.6f} ± {optimized_results['loss2_std']:.6f}")
    print(f"Loss3 (总体): {optimized_results['loss3_mean']:.6f} ± {optimized_results['loss3_std']:.6f}")
    print(f"Loss3 MSE: {optimized_results['loss3_mse_mean']:.6f} ± {optimized_results['loss3_mse_std']:.6f}")
    print(f"Loss3 SCC: {optimized_results['loss3_scc_mean']:.6f} ± {optimized_results['loss3_scc_std']:.6f}")
    
    # 检查是否达到目标
    target_loss3 = 0.05
    if optimized_results['loss3_mean'] <= target_loss3:
        print(f"\n🎉 成功！Loss3已降低到目标值 {target_loss3} 以下！")
    else:
        print(f"\n⚠️  Loss3 ({optimized_results['loss3_mean']:.6f}) 仍高于目标值 {target_loss3}")
        print("建议进一步调整超参数或模型架构")
    
    # 运行测试并保存结果
    print("\n=== 运行完整测试 ===")
    trainer = pl.Trainer(
        accelerator='gpu' if torch.cuda.is_available() else 'cpu',
        devices=1,
        logger=False,
        enable_progress_bar=True
    )
    
    trainer.test(optimized_model, test_loader)
    
    # 保存性能报告
    results_df = pd.DataFrame([optimized_results])
    results_df.to_csv('c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\optimized_performance_report.csv', index=False)
    
    print("\n=== 测试完成 ===")
    print("结果已保存到:")
    print("- predicted_scAB.csv")
    print("- predicted_embeddings.h5ad")
    print("- optimized_performance_report.csv")
    
    # 如果有原始结果，进行对比
    try:
        # 这里可以加载原始模型的结果进行对比
        # 为了演示，我们创建一个假设的原始结果
        original_results = {
            'loss1_mean': 0.1,  # 假设原始head1效果好
            'loss2_mean': 0.5,  # 假设原始head2效果差
            'loss3_mean': 0.3,  # 假设原始loss3较高
            'loss3_mse_mean': 0.2,
            'loss3_scc_mean': 0.4
        }
        
        plot_loss_comparison(
            original_results, 
            optimized_results, 
            'c:\\Users\\<USER>\\Desktop\\调整参数\\result\\Module3_result\\loss_comparison.png'
        )
        
    except Exception as e:
        print(f"生成对比图时出错: {e}")

if __name__ == '__main__':
    main()